<template>
    <div class="height-100 display-flex flex-column">
        <div class="b-margin-16 back-color-white">
            <div class="all-padding-16">
                <SearchBox
                    :searchOptionKey="'POLICY_SEARCH_OPTIONS'"
                    @updateSearchParams="updateSearchParams"
                >
                </SearchBox>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import SearchBox from '@/components/common/SearchBox.vue'
</script>


<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
</style>
